# Badge 2026 Infrastructure - Deployment

# Connecting

## Nebula
The cluster is connected via nebula VPN. There is a script / tool that I use to generate IPs and certs, this script deploys to our digital ocean bucket storage.

Once you have a nebula cert / ip / config, installed nebula (or download the nebula client), you can connect to the cluster.

I advise using the nebula VPN because otherhwise all infra updates go through rancher and then via the internal rancher proxy.
What can happen and is frustrating is that the proxy can be flaky and you'll get random connection resets / timeouts, which invalidate the pulumi updates.

```bash
# connect to the nebula network
nebula -config C:\cackalackycon\nebula\config.yaml
```

Once connected to the VPN, you can connect to the cluster using kubectl.

```bash
# connect to the cluster using a custom yaml, this is what I call mine... do w/e you want
kubectl --kubeconfig C:\cackalackycon\rke2-direct.yaml get nodes
```

## Kubeconfig
```bash
# get the kubeconfig from the cluster
# this is specific to rke2
scp root@<server>:/etc/rancher/rke2/rke2.yaml C:\cackalackycon\rke2-direct.yaml
```

# Deploying

```bash
doppler setup
# select "cackalacky-infra" as the project
# select dev as the config (dev is prod)
doppler run -- pulumi up --stack ckc26 -y
```