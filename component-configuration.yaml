cloudflare-tunnel:
  tunnel-name: badgenet-tunnel

namespaces:
  - name: cackalacky
    labels:
      environment: prod
      team: masochists
    annotations:
      owner: ckc-devs
  - name: platform
    labels:
      environment: prod
      team: masochists
    annotations:
      purpose: observability
  - name: health-check
  - name: longhorn-system
  - name: cert-manager
    depends_on:
      - cackalacky
  - name: external-secrets
  - name: argocd
  - name: emqx
  - name: monitoring
  - name: metallb-system
  - name: cache
  - name: kafka
  - name: doppler-operator-system
    managed_by_helm: True

emberstack-reflector:
  release-name: reflector-release
  chart: reflector
  repo: https://emberstack.github.io/helm-charts
  version: 9.1.17

longhorn:
  release-name: longhorn-release
  chart: longhorn
  repo: https://charts.longhorn.io
  version: 1.9.0
  namespace: longhorn-system

cert-manager:
  release-name: cert-manager-release
  chart: cert-manager
  repo: https://charts.jetstack.io
  version: v1.18.2
  namespace: cert-manager
  values:
    installCRDs: true
  secrets:
    cloudflare-global-api-key:
      name: cloudflare-global-api-key
      type: Opaque
      stringData:
        api-key: ${CLOUDFLARE_GLOBAL_API_KEY}
  clusterissuers:
    - name: letsencrypt-prod
      namespace: cert-manager
      acme:
        server: https://acme-v02.api.letsencrypt.org/directory
        email: <EMAIL>
        privateKeySecretRef:
          name: letsencrypt-prod
        solvers:
          - dns01:
              cloudflare:
                email: <EMAIL>
                apiKeySecretRef:
                  name: cloudflare-global-api-key
                  key: api-key
      depends_on:
        - cert-manager-release
        - cloudflare-global-api-key

external-secret-operator:
  release-name: external-secrets-operator-release
  chart: external-secrets
  repo: https://charts.external-secrets.io
  version: 0.19.0
  namespace: external-secrets
  secrets:
    aws-credentials:
      name: awssm-secret
      type: Opaque
      stringData:
        access-key: ${AWS_ACCESS_KEY}
        secret-access-key: ${AWS_SECRET_KEY}
  generators:
    - name: aws-ecr-gen
      kind: ECRAuthorizationToken
      apiVersion: generators.external-secrets.io/v1alpha1
      region: us-east-1
      secretRef:
        secretName: awssm-secret
        accessKeyKey: access-key
        secretKeyKey: secret-access-key
  externalsecrets:
    - name: ecr-externalsecret
      apiVersion: external-secrets.io/v1
      kind: ExternalSecret
      targetName: ecr-secret
      refreshInterval: 1h
      annotations:
        reflector.v1.k8s.emberstack.com/reflection-allowed: "true"
        reflector.v1.k8s.emberstack.com/reflection-auto-enabled: "true"
        reflector.v1.k8s.emberstack.com/reflection-allowed-namespaces: "cackalacky,health-check,kafka"
      sourceGenerator:
        kind: ECRAuthorizationToken
        name: aws-ecr-gen
        apiVersion: generators.external-secrets.io/v1alpha1

argo-cd:
  release-name: argo-cd-release
  chart: argo-cd
  repo: https://argoproj.github.io/argo-helm
  version: 8.2.5
  namespace: argocd
  values:
    global:
      domain: argocd.cackalacky.ninja
    configs:
      params:
        server.insecure: true
        otlp.address: signoz-release-otel-collector.platform:4317

doppler-operator:
  release-name: doppler-kubernetes-operator-release
  chart: doppler-kubernetes-operator
  repo: https://helm.doppler.com
  version: 1.6.0
  namespace: doppler-operator-system
  secrets:
    ckc25-service-token-dev:
      name: doppler-ckc25-service-token-dev
      type: Opaque
      stringData:
        serviceToken: ${SERVICE_TOKEN_DEV}
      ignore_changes:
        - data
        - stringData
    ckc25-service-token-prod:
      name: doppler-ckc25-service-token-prod
      type: Opaque
      stringData:
        serviceToken: ${SERVICE_TOKEN_PRD}
      ignore_changes:
        - data
        - stringData
    doppler-service-token-secret-prod-kafka:
      name: doppler-service-token-prod-kafka
      type: Opaque
      stringData:
        serviceToken: ${SERVICE_TOKEN_PRD}
      ignore_changes:
        - data
        - stringData
  dopplerSecrets:
    - name: dopplerckc25-service-token-dev
      namespace: doppler-operator-system
      tokenSecretRef: doppler-ckc25-service-token-dev
      managedSecret:
        name: doppler-ckc25-dev
        namespace: cackalacky

    - name: dopplerckc25-service-token-prod
      namespace: doppler-operator-system
      tokenSecretRef: doppler-ckc25-service-token-prod
      managedSecret:
        name: doppler-ckc25-prod
        namespace: cackalacky

    - name: doppler-service-token-crd-prod-kafka
      namespace: doppler-operator-system
      tokenSecretRef: doppler-service-token-prod-kafka
      managedSecret:
        name: doppler-ckc25-prod
        namespace: kafka

emqx:
  namespace: emqx
  chart: emqx-operator
  repo: https://repos.emqx.io/charts
  version: 2.2.29
  release-name: emqx

  deploy:
    name: emqx
    apiVersion: apps.emqx.io/v2beta1
    kind: EMQX
    image: emqx/emqx:6.0.0-M1.202507-elixir
    injectLicense: true
    config: |
      listeners.tcp.default {
        bind = "0.0.0.0:1883"
      }
      listeners.ssl.default {
        bind = "0.0.0.0:8883"
      }
      dashboard {
        listeners {
          http {
            bind = "0.0.0.0:18083"
            max_connections = 512
          }
        }
        cors = false
      }

    coreTemplate:
      replicas: 1
      storage:
        accessModes: [ReadWriteOnce]
        storageClassName: longhorn
        requests:
          storage: 10Gi

metallb:
  release-name: metal-lb-release
  chart: metallb
  repo: https://metallb.github.io/metallb
  version: 0.15.2
  namespace: metallb-system
  ignore_changes:
    - spec.template.spec.containers


cronjobs:
  - path: ./yamls/cron/refresh-ecr.yaml