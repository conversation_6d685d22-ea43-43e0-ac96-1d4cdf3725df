[tool.poetry]
name = "cackalacky"
version = "1.0.0"
description = "2025"
authors = ["<PERSON> <<EMAIL>>"]
readme = "README.md"
packages = []

[tool.poetry.dependencies]
python = "^3.11"
pyyaml = "^6.0.2"
pulumi = "^3.171"
# not pinning b/c I don't know the versions and don't care
pulumi-kubernetes = "*"
python-dotenv = "*"
pulumi-cloudflare = "*"
pulumi-docker = "*"
pulumi-kubernetes-cert-manager = "*"

[tool.poetry.group.dev.dependencies]
flake8 = "^7.1"
isort = "^5.13"
vulture = "^2.5"
black = "^24.10"
ipdb = "^0.13.13"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

# https://black.readthedocs.io/en/stable/usage_and_configuration/the_basics.html
[tool.black]
line-length = 150
exclude = ".*.sql|.*.md|.*.pyc|.*.yaml|.*.yml|.*.json|registration|do-not-commit|venv"

# https://pycqa.github.io/isort/docs/configuration/options.html
[tool.isort]
profile = "black"
float_to_top = true
skip = [
    ".gitignore",
    ".dockerignore",
    ".venv"
]
skip_glob = [
    "**/.venv/**",
    "**/registration/**"
]
extend_skip = [".venv"]
