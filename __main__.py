import base64
import os
from pathlib import Path

import pulumi
import pulumi_kubernetes_cert_manager as cert_manager
import yaml as pyyaml
from dotenv import load_dotenv
from pulumi_cloudflare import Provider
from pulumi_kubernetes import Provider as KUBE_Provider
from pulumi_kubernetes import apiextensions, helm, yaml
from pulumi_kubernetes.apps.v1 import Deployment, DeploymentSpecArgs
from pulumi_kubernetes.core.v1 import (
    HTTPGetActionArgs,
    Namespace,
    PersistentVolumeClaim,
    PersistentVolumeClaimSpecArgs,
)
from pulumi_kubernetes.core.v1 import Secret as K8S_Secret
from pulumi_kubernetes.core.v1 import (
    Service,
    ServicePortArgs,
    ServiceSpecArgs,
    VolumeResourceRequirementsArgs,
)
from pulumi_kubernetes.core.v1.outputs import (
    ContainerPort,
    EnvVar,
    PersistentVolumeClaimVolumeSource,
    Volume,
    VolumeMount,
)
from pulumi_kubernetes.yaml import ConfigFile
from pulumi_kubernetes.meta.v1 import LabelSelectorArgs, ObjectMetaArgs
from pulumi_kubernetes.meta.v1.outputs import LabelSelector

from certs.cert_manager import CERT_MANAGER_NS_STR, CERT_MANAGER_VERSION

# Import the cloudflared deployment function
from cloudflare.deployment import create_cloudflared_deployment
from cloudflare.tunnel import create_tunnel, get_existing_tunnel
from intranet.nginx import nginx_configmap_obj, nginx_intranet_deployment

# Import the namespace creation function
from k8s.namespaces import create_namespace


"""
Cloudflare Specific Items
"""
cloudflare_api_token = os.getenv("CLOUDFLARE_API_TOKEN")
cloudflare_tunnel_secret = os.getenv("CLOUDFLARE_TUNNEL_SECRET")
cloudflare_account_id = os.getenv("CLOUDFLARE_ACCOUNT_ID")
cloudflare_zone_id = os.getenv("CLOUDFLARE_ZONE_ID")

# Create a Cloudflare provider instance with the API token
cloudflare_provider = Provider("cloudflareProvider", api_token=cloudflare_api_token)

# Convert raw secret to base64
cloudflare_tunnel_secret_base64_encoded = base64.b64encode(cloudflare_tunnel_secret.encode("utf-8"))
cloudflare_tunnel_secret_base64_encoded_str = str(cloudflare_tunnel_secret_base64_encoded, "utf-8")

"""
K8S Specific Setup
 - The following will setup pods, containers, etc in the k8s cluster
"""
# Read the kubeconfig file
with open("C:\\cackalacky\\rke2-direct.yaml", "r") as kubeconfig_file:
    kubeconfig = kubeconfig_file.read()

# set up provider
k8s_provider = KUBE_Provider("k8s-provider", kubeconfig=kubeconfig)

with open(Path(__file__).parent / "component-configuration.yaml", "r") as f:
    config = pyyaml.safe_load(f)

resource_registry = {}

def resolve_dependencies(depends_on_list, registry):
    return [registry[name] for name in depends_on_list if name in registry]

namespaces_config = config.get("namespaces", [])
namespace_objs = {}
# First pass: create all namespace objects (without depends_on yet)
for ns in namespaces_config:
    name = ns["name"]
    namespace_objs[name] = None  # reserve key for dependency lookup

for ns in namespaces_config:
    name = ns["name"]
    depends = ns.get("depends_on", [])
    labels = ns.get("labels", {})
    annotations = ns.get("annotations", {})
    depends_objs = [namespace_objs[d] for d in depends if d in namespace_objs]

    managed_by_helm = ns.get("managed_by_helm", False)
    if managed_by_helm:
        continue

    ns_obj = Namespace(
        name,
        metadata=ObjectMetaArgs(
            name=name,
            labels=labels,
            annotations=annotations,
        ),
        opts=pulumi.ResourceOptions(
            provider=k8s_provider,
            depends_on=depends_objs,
        ),
    )

    namespace_objs[name] = ns_obj

    pulumi.export(f"namespace_{name.replace('-', '_')}", ns_obj.metadata["name"])


cf_tunnel_name = config["cloudflare-tunnel"]["tunnel-name"]
try:
    tunnel = create_tunnel(
        cloudflare_provider=cloudflare_provider,
        account_id=cloudflare_account_id,
        tunnel_name=cf_tunnel_name,
        secret=cloudflare_tunnel_secret_base64_encoded_str,
    )
except Exception as e:
    print(f"Failed to create tunnel: {e}")
    tunnel = get_existing_tunnel(cloudflare_provider=cloudflare_provider, account_id=cloudflare_account_id, tunnel_name=cf_tunnel_name)


cloudflared_deployment = create_cloudflared_deployment(
    namespace=namespace_objs["cackalacky"].metadata["name"],
    tunnel_token=tunnel.tunnel_token,
    k8s_provider=k8s_provider,
    depends_on=[namespace_objs["cackalacky"]]
)
pulumi.export("cloudflared_deployment_name", cloudflared_deployment.metadata["name"])


""" emberstack/reflector """
reflector_config = config["emberstack-reflector"]
reflector_release = helm.v3.Release(
    reflector_config['release-name'],
    chart=reflector_config['chart'],
    repository_opts=helm.v3.RepositoryOptsArgs(repo=reflector_config['repo']),
    version=reflector_config['version'],
    opts=pulumi.ResourceOptions(provider=k8s_provider),
)
pulumi.export("reflector_release_name", reflector_release.name)

""" Longhorn """
longhorn_config = config["longhorn"]
longhorn_release = helm.v3.Release(
    longhorn_config['release-name'],
    chart=longhorn_config['chart'],
    repository_opts=helm.v3.RepositoryOptsArgs(repo=longhorn_config['repo']),
    version=longhorn_config['version'],
    namespace=longhorn_config['namespace'],
    opts=pulumi.ResourceOptions(
        provider=k8s_provider,
        depends_on=[
            namespace_objs["cackalacky"],
            namespace_objs["longhorn-system"]
        ]
    ),
)
pulumi.export("longhorn_release_name", longhorn_release.name)

""" Cert Manager """
# Load cert-manager config
cert_manager_cfg = config["cert-manager"]

# Deploy cert-manager using the Helm chart
cert_manager_release = helm.v3.Chart(
    cert_manager_cfg["release-name"],
    config=helm.v3.ChartOpts(
        chart=cert_manager_cfg["chart"],
        version=cert_manager_cfg["version"].lstrip("v"),  # remove leading 'v' if needed
        fetch_opts=helm.v3.FetchOpts(
            repo=cert_manager_cfg["repo"],
        ),
        namespace=cert_manager_cfg["namespace"],
        values=cert_manager_cfg.get("values", {}),
    ),
    opts=pulumi.ResourceOptions(
        provider=k8s_provider,
        depends_on=[
            namespace_objs["cert-manager"]
        ],
    ),
)
resource_registry["cert-manager-release"] = cert_manager_release

secrets_cfg = cert_manager_cfg.get("secrets", {})
for secret_id, secret_data in secrets_cfg.items():
    # os.path.expandvars("${FOO}") replaces ${FOO} with the value of os.environ["FOO"].
    string_data = {
        k: os.path.expandvars(v) for k, v in secret_data.get("stringData", {}).items()
    }

    secret = K8S_Secret(
        secret_id,
        api_version="v1",
        kind="Secret",
        metadata={
            "name": secret_data.get("name", secret_id),
            "namespace": namespace_objs["cert-manager"].metadata["name"],
        },
        type=secret_data.get("type", "Opaque"),
        string_data=string_data,
        opts=pulumi.ResourceOptions(
            provider=k8s_provider,
            depends_on=[cert_manager_release],
        ),
    )
    resource_registry[secret] = secret

clusterissuers = cert_manager_cfg.get("clusterissuers", [])
for issuer in clusterissuers:
    issuer_name = issuer["name"]
    depends = resolve_dependencies(issuer.get("depends_on", []), resource_registry)
    apiextensions.CustomResource(
        f"{issuer['name']}-clusterissuer",
        api_version="cert-manager.io/v1",
        kind="ClusterIssuer",
        metadata={
            "name": issuer["name"],
            # Note: ClusterIssuers are cluster-scoped, so namespace is often unnecessary.
            # Including it only if explicitly provided:
            **({"namespace": issuer["namespace"]} if "namespace" in issuer else {})
        },
        spec={"acme": issuer["acme"]},
        opts=pulumi.ResourceOptions(
            provider=k8s_provider,
            depends_on=depends,
        ),
    )


""" External Secret Operator """
eso_config = config["external-secret-operator"]
namespace_external_secrets = eso_config["namespace"]
eso_release = helm.v3.Release(
    eso_config["release-name"],
    helm.v3.ReleaseArgs(
        chart=eso_config["chart"],
        version=eso_config["version"],
        repository_opts=helm.v3.RepositoryOptsArgs(
            repo=eso_config["repo"],
        ),
        namespace=namespace_external_secrets,
    ),
    opts=pulumi.ResourceOptions(provider=k8s_provider, depends_on=[namespace_objs["external-secrets"]]),
)

eso_secrets = eso_config.get("secrets", {})
generators = eso_config.get("generators", {})
externalsecrets = eso_config.get("externalsecrets", [])

secret_objs = {}
for secret_id, secret in eso_secrets.items():
    string_data = {}
    for k, v in secret.get("stringData", {}).items():
        # If value is a reference to an env var (${FOO}), resolve it at runtime
        if isinstance(v, str) and v.startswith("${") and v.endswith("}"):
            env_var = v[2:-1]
            string_data[k] = os.environ[env_var]
        else:
            string_data[k] = v  # Static value, not an env reference

    k8s_secret = K8S_Secret(
        secret["name"],
        metadata=ObjectMetaArgs(
            name=secret["name"],
            namespace=namespace_external_secrets,
        ),
        type=secret.get("type", "Opaque"),
        string_data=string_data,
        opts=pulumi.ResourceOptions(
            provider=k8s_provider,
            depends_on=[namespace_objs["external-secrets"], eso_release],
        ),
    )
    secret_objs[secret["name"]] = k8s_secret


for gen in generators:
    if gen["kind"] == "ECRAuthorizationToken":
        linked_secret_name = gen["secretRef"]["secretName"]
        linked_secret = secret_objs[linked_secret_name]

        apiextensions.CustomResource(
            gen["name"],
            api_version=gen["apiVersion"],
            kind=gen["kind"],
            metadata=ObjectMetaArgs(name=gen["name"], namespace=namespace_external_secrets),
            spec={
                "region": gen["region"],
                "auth": {
                    "secretRef": {
                        "accessKeyIDSecretRef": {
                            "name": linked_secret.metadata.name,
                            "key": gen["secretRef"]["accessKeyKey"],
                        },
                        "secretAccessKeySecretRef": {
                            "name": linked_secret.metadata.name,
                            "key": gen["secretRef"]["secretKeyKey"],
                        },
                    }
                },
            },
            opts=pulumi.ResourceOptions(
                provider=k8s_provider,
                depends_on=[linked_secret, namespace_objs["external-secrets"], eso_release],
            ),
        )

for es in externalsecrets:
    generator_ref = es["sourceGenerator"]

    generator_name = generator_ref["name"]
    if generator_name not in [g["name"] for g in generators]:
        raise RuntimeError(f"Generator {generator_name} not defined in config.")

    annotations = es.get("annotations", {})

    apiextensions.CustomResource(
        es["name"],
        api_version=es["apiVersion"],
        kind=es["kind"],
        metadata=ObjectMetaArgs(
            name=es["name"],
            namespace=namespace_external_secrets,
        ),
        spec={
            "refreshInterval": es.get("refreshInterval", "1h"),
            "target": {
                "name": es["targetName"],
                "creationPolicy": "Owner",
                "template": {
                    "type": "",
                    "metadata": {
                        "annotations": annotations,
                    },
                },
            },
            "dataFrom": [
                {
                    "sourceRef": {
                        "generatorRef": {
                            "apiVersion": generator_ref["apiVersion"],
                            "kind": generator_ref["kind"],
                            "name": generator_ref["name"],
                        }
                    }
                }
            ],
        },
        opts=pulumi.ResourceOptions(
            provider=k8s_provider,
            depends_on=[namespace_objs["external-secrets"], eso_release],
        ),
    )


""" Argo CD """
argocd_config = config["argo-cd"]
namespace_argocd = argocd_config["namespace"]
argocd_release = helm.v3.Release(
    argocd_config["release-name"],
    helm.v3.ReleaseArgs(
        chart=argocd_config["chart"],
        version=argocd_config["version"],
        repository_opts=helm.v3.RepositoryOptsArgs(
            repo=argocd_config["repo"],
        ),
        namespace=namespace_argocd,
        values=argocd_config.get("values", {})
    ),
    opts=pulumi.ResourceOptions(provider=k8s_provider, depends_on=[namespace_objs["external-secrets"]]),
)

pulumi.export("namespace_name_argocd", namespace_objs["argocd"].metadata['name'])


""" Doppler """
doppler_config = config["doppler-operator"]
namespace_doppler_operator_system = doppler_config["namespace"]
# doppler helm chart expects the namespace to not exist
doppler_kubernetes_operator_release = helm.v3.Release(
    doppler_config["release-name"],
    helm.v3.ReleaseArgs(
        chart=doppler_config["chart"],
        version=doppler_config["version"],
        repository_opts=helm.v3.RepositoryOptsArgs(
            repo=doppler_config["repo"],
        )
    ),
    opts=pulumi.ResourceOptions(provider=k8s_provider, depends_on=[]),
)

doppler_secrets = doppler_config.get("secrets", {})
for secret_id, secret_cfg in doppler_secrets.items():
    string_data = {}
    for k, v in secret_cfg.get("stringData", {}).items():
        if isinstance(v, str) and v.startswith("${") and v.endswith("}"):
            env_var = v[2:-1]
            string_data[k] = os.environ[env_var]
        else:
            string_data[k] = v

    ignore_changes = secret_cfg.get("ignore_changes", [])

    doppler_secret = K8S_Secret(
        secret_cfg["name"],
        metadata=ObjectMetaArgs(
            name=secret_cfg["name"],
            namespace=namespace_doppler_operator_system,
        ),
        type=secret_cfg.get("type", "Opaque"),
        string_data=string_data,
        opts=pulumi.ResourceOptions(
            provider=k8s_provider,
            depends_on=[doppler_kubernetes_operator_release],
            ignore_changes=ignore_changes,
        ),
    )
    resource_registry[secret_cfg["name"]] = doppler_secret


doppler_crds = doppler_config.get("dopplerSecrets", [])

for crd in doppler_crds:
    token_secret_name = crd["tokenSecretRef"]
    managed_secret = crd["managedSecret"]
    depends_on_secret = resource_registry.get(token_secret_name)

    apiextensions.CustomResource(
        crd["name"],
        api_version="secrets.doppler.com/v1alpha1",
        kind="DopplerSecret",
        metadata=ObjectMetaArgs(
            name=crd["name"],
            namespace=crd["namespace"],
        ),
        spec={
            "tokenSecret": {
                "name": token_secret_name,
            },
            "managedSecret": {
                "name": managed_secret["name"],
                "namespace": managed_secret["namespace"],
            },
        },
        opts=pulumi.ResourceOptions(
            provider=k8s_provider,
            depends_on=[depends_on_secret] if depends_on_secret else [],
        ),
    )


emqx_cfg = config.get("emqx", {})
emqx_namespace = emqx_cfg["namespace"]

# Helm chart
emqx_operator_release = helm.v3.Release(
    emqx_cfg["release-name"],
    helm.v3.ReleaseArgs(
        chart=emqx_cfg["chart"],
        version=emqx_cfg["version"],
        repository_opts=helm.v3.RepositoryOptsArgs(
            repo=emqx_cfg["repo"],
        ),
        namespace=emqx_namespace,
        name=emqx_cfg["release-name"],
    ),
    opts=pulumi.ResourceOptions(
        provider=k8s_provider,
        depends_on=[namespace_objs[emqx_namespace]],
    ),
)

# EMQX Custom Resource
deploy_cfg = emqx_cfg["deploy"]
raw_config = deploy_cfg["config"]

if deploy_cfg.get("injectLicense"):
    license_val = os.environ.get("EMQX_LIC")
    if not license_val:
        raise Exception("Missing EMQX_LIC in environment for license injection")
    raw_config = raw_config.replace("${EMQX_LIC}", license_val)

apiextensions.CustomResource(
    deploy_cfg["name"],
    api_version=deploy_cfg["apiVersion"],
    kind=deploy_cfg["kind"],
    metadata=ObjectMetaArgs(
        name=deploy_cfg["name"],
        namespace=emqx_namespace,
    ),
    spec={
        "image": deploy_cfg["image"],
        "config": {
            "data": raw_config,
        },
        "coreTemplate": {
            "spec": {
                "volumeClaimTemplates": {
                    "accessModes": deploy_cfg["coreTemplate"]["storage"]["accessModes"],
                    "storageClassName": deploy_cfg["coreTemplate"]["storage"]["storageClassName"],
                    "resources": {
                        "requests": {
                            "storage": deploy_cfg["coreTemplate"]["storage"]["requests"]["storage"],
                        }
                    },
                },
                "replicas": deploy_cfg["coreTemplate"]["replicas"],
            }
        },
    },
    opts=pulumi.ResourceOptions(
        provider=k8s_provider,
        depends_on=[emqx_operator_release, namespace_objs[emqx_namespace]],
    ),
)

# MetalLB
metallb_cfg = config.get("metallb", {})
metallb_namespace = metallb_cfg["namespace"]

metallb_release = helm.v3.Release(
    metallb_cfg["release-name"],
    helm.v3.ReleaseArgs(
        chart=metallb_cfg["chart"],
        version=metallb_cfg["version"],
        repository_opts=helm.v3.RepositoryOptsArgs(
            repo=metallb_cfg["repo"],
        ),
        namespace=metallb_namespace,
        name=metallb_cfg["release-name"],
    ),
    opts=pulumi.ResourceOptions(
        provider=k8s_provider,
        depends_on=[namespace_objs[metallb_namespace]],
        ignore_changes=metallb_cfg.get("ignore_changes", []),
    ),
)

cron_configs = config.get("cronjobs", [])

for cron in cron_configs:
    ConfigFile(
        cron["path"],
        file=cron["path"],
        opts=pulumi.ResourceOptions(provider=k8s_provider),
    )

### REMAINDER OF THINGS TO DEPLOY ARE IN rest_of_main_to_deploy.py ###
