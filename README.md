# Pinball Infrastructure

> What this doesn't include is spinning up the k8s cluster itself.
> All of these commands require an existing k8s cluster.

# Getting Started

1. Install pulumi
   1. https://www.pulumi.com/docs/iac/download-install/#chocolatey
2. Install pyenv (or just use w/e python version you have):
   1. https://github.com/pyenv-win/pyenv-win/blob/master/docs/installation.md#powershell
   2. WINDOWS: https://github.com/pyenv-win/pyenv-win/blob/master/docs/installation.md#add-system-settings
3. Check your pyenv version:
   1. pyenv version
4. Update pyenv packages
   1. ``pyenv update``
5. Install python 3.13
```bash
pyenv install 3.13.5
pyenv local 3.13.5
```
6. Install dependencies
```bash
poetry lock
poetry install
```
7. Install doppler and use the cackalacky-infra project (dev)
```bash
doppler setup
# select "cackalacky-infra" as the project
# select dev as the config (dev is prod)
doppler run -- pulumi up --stack ckc26 -y
```

# Pulumi commands

```shell

pulumi stack init ckc26
# make sure you select the cackalacky doppler project
doppler run -- pulumi up --stack ckc26 -y

pulumi cancel
```

## Helpful commands
```bash
pulumi cancel
pulumi state delete urn:pulumi:cackalacky25::cacklacky::kubernetes:core/v1:Namespace::<resource>
pulumi state delete urn:pulumi:cackalacky25::cacklacky::kubernetes:batch/v1:Job::platform/signoz-release-schema-migrator-async-init
```

# Helm Commands:
```bash

helm repo add cloudflare https://cloudflare.github.io/helm-charts 
helm repo add longhorn https://charts.longhorn.io 
helm repo add emberstack https://emberstack.github.io/helm-charts 
helm repo add argo https://argoproj.github.io/argo-helm
helm repo add external-secrets https://charts.external-secrets.io
helm repo add grafana https://grafana.github.io/helm-charts
helm repo add emqx https://repos.emqx.io/charts
helm repo add signoz https://charts.signoz.io
```

## List repos
```bash
helm repo list
```

## Update repos

```bash
helm repo update
```

# Connecting

## Nebula
The cluster is connected via nebula VPN. There is a script / tool that I use to generate IPs and certs, this script deploys to our digital ocean bucket storage.

Once you have a nebula cert / ip / config, installed nebula (or download the nebula client), you can connect to the cluster.

I advise using the nebula VPN because otherhwise all infra updates go through rancher and then via the internal rancher proxy.
What can happen and is frustrating is that the proxy can be flaky and you'll get random connection resets / timeouts, which invalidate the pulumi updates.

```bash
# connect to the nebula network
nebula -config C:\cackalackycon\nebula\config.yaml
```

Once connected to the VPN, you can connect to the cluster using kubectl.

```bash
# connect to the cluster using a custom yaml, this is what I call mine... do w/e you want
kubectl --kubeconfig C:\cackalackycon\rke2-direct.yaml get nodes
```

## Kubeconfig
```bash
# get the kubeconfig from the cluster
# this is specific to rke2
scp root@<server>:/etc/rancher/rke2/rke2.yaml C:\cackalackycon\rke2-direct.yaml
```